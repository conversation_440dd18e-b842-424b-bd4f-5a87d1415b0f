import type { PromotionGroup } from '@/libs/orders/types';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import { PromotionItemContent } from './PromoItemContent/PromoItemContent';

interface OrderHistoryPromoItemProps {
  promotionGroup: PromotionGroup;
}

export const OrderHistoryPromoItem = ({
  promotionGroup,
}: OrderHistoryPromoItemProps) => {
  const { promotion, items } = promotionGroup;

  if (!promotion || items.length === 0) return null;

  const firstItem = items[0];

  return (
    <ProductCartHorizontal
      product={firstItem.product}
      productOfferId={firstItem.productOfferId || ''}
      content={<PromotionItemContent promotionGroup={promotionGroup} />}
    />
  );
};
