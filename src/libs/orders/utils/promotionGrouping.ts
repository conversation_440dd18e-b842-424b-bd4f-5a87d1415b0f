import type {
  OrderHistoryDetailItemType,
  OrderHistoryDetailPromotionItemType,
  OrderHistoryPromotion,
  OrderHistoryPromotionAppliedBenefit,
  VendorPromotionGroups,
} from '../types';

export const getVendorPromotions = ({
  id,
  promotions,
}: {
  id: string;
  promotions: OrderHistoryPromotion[];
}) => {
  if (!promotions) return [];
  return promotions.filter((promotion) => promotion.vendor?.id === id);
};

const getMinimumQuantity = (promotion: OrderHistoryPromotion) => {
  const minimumQuantityCondition = promotion.appliedRules
    .flatMap((rule) => rule.conditions)
    .find((condition) => condition.type === 'minimum_quantity');

  return minimumQuantityCondition
    ? +minimumQuantityCondition.config.quantity
    : 1;
};

const getFreeBenefit = (promotion: OrderHistoryPromotion) => {
  const freeBenefit = promotion.appliedBenefits.find(
    (benefit) => benefit.type === 'give_free_product',
  ) as OrderHistoryPromotionAppliedBenefit;
  return freeBenefit;
};

export const getItemDetails = ({
  item,
  promotion,
}: {
  item: OrderHistoryDetailItemType;
  promotion: OrderHistoryPromotion;
}) => {
  const freeBenefit = getFreeBenefit(promotion);
  const triggerMinQty = getMinimumQuantity(promotion);
  const triggeredTimes = Math.floor(item.quantity / triggerMinQty);
  const freeQtyPerTrigger = +freeBenefit.quantity;
  const freeItemsQty = triggeredTimes * freeQtyPerTrigger;

  return {
    subtotalPaidItems: +item.totalPrice,
    subtotalAllItems: +item.totalPrice + freeItemsQty * +item.unitPrice,
    paidItemsQty: item.quantity,
    freeItemsQty,
  };
};

type TriggeredItem = {
  items: OrderHistoryDetailItemType[];
  subtotalPaidItems: number;
  subtotalAllItems: number;
  paidItemsQty: number;
  freeItemsQty: number;
};
export const groupBXGY = ({
  items,
  promotion,
}: {
  items: OrderHistoryDetailItemType[];
  promotion: OrderHistoryPromotion;
}) => {
  const freeBenefit = getFreeBenefit(promotion);

  const triggeringIds = promotion.triggeringItems.map(
    ({ productOfferId }) => productOfferId,
  );
  const triggeringItems = items.filter((item) =>
    triggeringIds.includes(item.productOfferId),
  );

  return triggeringItems.reduce(
    (acc, item) => {
      const detail = getItemDetails({ item, promotion });

      return {
        subtotalPaidItems: acc.subtotalPaidItems + detail.subtotalPaidItems,
        subtotalAllItems: acc.subtotalAllItems + detail.subtotalAllItems,
        paidItemsQty: acc.paidItemsQty + detail.paidItemsQty,
        freeItemsQty: acc.freeItemsQty + detail.freeItemsQty,
        promotion,
        items: [
          ...acc.items,
          item,
          {
            id: `${item.id}-free`,
            quantity: detail.freeItemsQty,
            unitPrice: undefined,
            totalPrice: undefined,
            productOfferId: freeBenefit.freeOffer?.id || item.productOfferId,
            product: {
              ...item.product,
              name: freeBenefit.freeOffer?.name || item.product.name,
            },
            status: undefined,
          },
        ],
      };
    },
    {
      items: [] as OrderHistoryDetailItemType[],
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
    } as TriggeredItem,
  );
};

const promotionsSetter = {
  buy_x_get_y: groupBXGY,
};

const untriggeredInAllPromotions = ({
  triggeredItems,
  items,
}: {
  triggeredItems: TriggeredItem[];
  items: OrderHistoryDetailItemType[];
}) => {
  const flattenedTriggered = triggeredItems.flatMap((item) => item.items);
  const flattenedTriggeredIds = new Set(
    flattenedTriggered.map((item) => item.id),
  );
  return items.filter((item) => !flattenedTriggeredIds.has(item.id));
};

export const groupByPromotions = ({
  items,
  promotions,
}: {
  items: OrderHistoryDetailItemType[];
  promotions: OrderHistoryPromotion[];
}) => {
  const { allTriggeredItems, ...allPromotions } = Object.entries(
    promotionsSetter,
  ).reduce(
    (acc, [promotionType, setter]) => {
      const promotionsByType = promotions.filter(
        (promotion) => promotion.type === promotionType,
      );
      if (!promotionsByType) return acc;

      const triggeredItems = promotionsByType.map((promotion) =>
        setter({ items, promotion }),
      );
      const allTriggeredItems = [
        ...(acc.allTriggeredItems || []),
        ...triggeredItems,
      ];

      return {
        ...acc,
        allTriggeredItems,
        [promotionType]: triggeredItems,
      };
    },
    {} as VendorPromotionGroups & { allTriggeredItems?: TriggeredItem[] },
  );

  const allUntriggeredItems = untriggeredInAllPromotions({
    triggeredItems: allTriggeredItems || [],
    items,
  });

  return {
    ...allPromotions,
    untriggeredItems: allUntriggeredItems,
  } as VendorPromotionGroups;
};
